# 任务辅助达成机 - API接口文档

## 基础信息

**Base URL**: `http://localhost:3000/api/v1`
**认证方式**: <PERSON><PERSON> (JWT)
**Content-Type**: `application/json`

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求参数**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "confirmPassword": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "id": "user_id",
      "username": "username",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
}
```

### 用户登录
```http
POST /auth/login
```

**请求参数**:
```json
{
  "email": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "user_id",
      "username": "username",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
}
```

## 灵感管理接口

### 创建灵感
```http
POST /inspirations
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "content": "string",
  "type": "text|voice|image|link",
  "tags": ["string"],
  "attachments": ["string"]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "灵感创建成功",
  "data": {
    "id": "inspiration_id",
    "content": "灵感内容",
    "type": "text",
    "tags": ["标签1", "标签2"],
    "createdAt": "2025-08-28T10:00:00Z",
    "analysis": {
      "category": "工作职业",
      "importance": 0.8,
      "sentiment": "positive"
    }
  }
}
```

### 获取灵感列表
```http
GET /inspirations?page=1&limit=20&category=工作职业&search=关键词
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选
- `search`: 搜索关键词
- `sortBy`: 排序字段 (createdAt|importance)
- `sortOrder`: 排序方向 (asc|desc)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "inspirations": [
      {
        "id": "inspiration_id",
        "content": "灵感内容",
        "category": "工作职业",
        "importance": 0.8,
        "tags": ["标签1"],
        "createdAt": "2025-08-28T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### AI分析灵感
```http
POST /inspirations/{id}/analyze
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "category": "工作职业",
    "importance": 0.8,
    "sentiment": {
      "label": "POSITIVE",
      "score": 0.9
    },
    "keywords": ["关键词1", "关键词2"],
    "suggestedTags": ["建议标签1", "建议标签2"],
    "summary": "内容摘要"
  }
}
```

## 目标管理接口

### 创建目标
```http
POST /goals
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "title": "string",
  "description": "string",
  "type": "总目标|平行目标|分目标|阶段目标",
  "priority": "高|中|低",
  "deadline": "2025-12-31T23:59:59Z",
  "parentGoal": "parent_goal_id",
  "metrics": {
    "target": 100,
    "unit": "个"
  },
  "tags": ["string"]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "目标创建成功",
  "data": {
    "id": "goal_id",
    "title": "目标标题",
    "description": "目标描述",
    "type": "总目标",
    "priority": "高",
    "status": "未开始",
    "progress": 0,
    "deadline": "2025-12-31T23:59:59Z",
    "createdAt": "2025-08-28T10:00:00Z"
  }
}
```

### 获取目标列表
```http
GET /goals?type=总目标&status=进行中&page=1&limit=20
Authorization: Bearer {token}
```

**查询参数**:
- `type`: 目标类型筛选
- `status`: 状态筛选
- `priority`: 优先级筛选
- `page`: 页码
- `limit`: 每页数量

### 更新目标进度
```http
PUT /goals/{id}/progress
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "progress": 75,
  "metrics": {
    "current": 75
  },
  "note": "进度更新说明"
}
```

### 目标可行性分析
```http
POST /goals/{id}/analyze
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "feasibility": 0.8,
    "difficulty": 0.6,
    "timeEstimate": 90,
    "resourceRequirement": "中等",
    "riskFactors": ["时间紧张", "资源不足"],
    "suggestions": ["建议1", "建议2"]
  }
}
```

## 任务管理接口

### 创建任务
```http
POST /tasks
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "title": "string",
  "description": "string",
  "goalId": "goal_id",
  "priority": "高|中|低",
  "importance": "高|低",
  "urgency": "高|低",
  "deadline": "2025-12-31T23:59:59Z",
  "estimatedTime": 120,
  "dependencies": ["task_id"]
}
```

### 获取任务列表
```http
GET /tasks?goalId=goal_id&status=未开始&priority=高
Authorization: Bearer {token}
```

### 更新任务状态
```http
PUT /tasks/{id}/status
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "status": "进行中|已完成|已暂停",
  "note": "状态更新说明"
}
```

### 获取优先级矩阵
```http
GET /tasks/priority-matrix
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "matrix": {
      "important_urgent": [
        {
          "id": "task_id",
          "title": "任务标题",
          "deadline": "2025-08-30T23:59:59Z"
        }
      ],
      "important_not_urgent": [],
      "not_important_urgent": [],
      "not_important_not_urgent": []
    }
  }
}
```

## 文档管理接口

### 上传文档
```http
POST /documents
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:
- `file`: 文件
- `title`: 标题
- `category`: 分类
- `tags`: 标签 (JSON字符串)

### 添加链接文档
```http
POST /documents/link
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "url": "https://example.com/article",
  "title": "文章标题",
  "category": "学习资料",
  "tags": ["标签1", "标签2"]
}
```

### 获取文档列表
```http
GET /documents?category=学习资料&search=关键词
Authorization: Bearer {token}
```

### 文档内容提取
```http
POST /documents/{id}/extract
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "title": "提取的标题",
    "content": "提取的内容",
    "summary": "内容摘要",
    "keywords": ["关键词1", "关键词2"],
    "category": "自动分类结果"
  }
}
```

## 数据分析接口

### 获取仪表板数据
```http
GET /analytics/dashboard
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalGoals": 10,
      "activeGoals": 5,
      "completedGoals": 3,
      "totalTasks": 25,
      "completedTasks": 15,
      "totalInspirations": 50
    },
    "progress": {
      "weeklyProgress": [
        {"date": "2025-08-22", "completed": 3},
        {"date": "2025-08-23", "completed": 2}
      ],
      "goalProgress": [
        {"goalId": "goal1", "title": "目标1", "progress": 75},
        {"goalId": "goal2", "title": "目标2", "progress": 50}
      ]
    },
    "insights": {
      "mostActiveCategory": "工作职业",
      "averageCompletionTime": 5.2,
      "productivityTrend": "上升"
    }
  }
}
```

### 获取个人画像
```http
GET /analytics/profile
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "personality": {
      "executionStyle": "计划型",
      "motivationType": "成就导向",
      "workPattern": "集中式"
    },
    "interests": [
      {"category": "技术学习", "score": 0.9},
      {"category": "职业发展", "score": 0.8}
    ],
    "strengths": ["目标明确", "执行力强"],
    "improvements": ["时间管理", "优先级判断"],
    "recommendations": [
      "建议使用番茄工作法提高专注度",
      "可以尝试将大目标分解为更小的里程碑"
    ]
  }
}
```

### 生成成长报告
```http
POST /analytics/report
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "period": "week|month|quarter|year",
  "startDate": "2025-08-01",
  "endDate": "2025-08-28"
}
```

## 通知接口

### 获取通知列表
```http
GET /notifications?unread=true&page=1&limit=20
Authorization: Bearer {token}
```

### 标记通知已读
```http
PUT /notifications/{id}/read
Authorization: Bearer {token}
```

### 获取提醒设置
```http
GET /notifications/settings
Authorization: Bearer {token}
```

### 更新提醒设置
```http
PUT /notifications/settings
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "taskReminders": true,
  "goalDeadlines": true,
  "dailyReview": true,
  "weeklyReport": true,
  "reminderTime": "09:00"
}
```

## 错误响应格式

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

**常见错误码**:
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `VALIDATION_ERROR`: 参数验证失败
- `INTERNAL_ERROR`: 服务器内部错误
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

## 状态码说明

- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率超限
- `500`: 服务器内部错误

---

**文档版本**: v1.0
**最后更新**: 2025-08-28
