# 任务辅助达成机 - 项目启动部署指南

## 快速开始

### 1. 环境要求

#### 基础环境
- Node.js >= 16.0.0
- Python >= 3.8
- MongoDB >= 4.4
- Redis >= 6.0
- Docker >= 20.10 (可选)

#### 开发工具
- VS Code 或其他IDE
- Git
- Postman (API测试)

### 2. 项目克隆和初始化

```bash
# 克隆项目
git clone <repository-url>
cd task-achievement-assistant

# 安装依赖
npm run install:all

# 或者手动安装
cd frontend && npm install
cd ../backend && npm install
cd ../ai-service && pip install -r requirements.txt
```

### 3. 环境配置

#### 后端环境变量 (.env)
```env
# 服务配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/task_assistant
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=7d

# AI服务配置
AI_SERVICE_URL=http://localhost:8001
OPENAI_API_KEY=your-openai-api-key

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

#### 前端环境变量 (.env)
```env
REACT_APP_API_BASE_URL=http://localhost:3000/api
REACT_APP_AI_SERVICE_URL=http://localhost:8001
REACT_APP_UPLOAD_MAX_SIZE=10485760
REACT_APP_ENVIRONMENT=development
```

#### AI服务环境变量 (.env)
```env
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
REDIS_URL=redis://localhost:6379
MODEL_CACHE_DIR=./models
LOG_LEVEL=INFO
```

### 4. 数据库初始化

#### MongoDB初始化脚本
```javascript
// scripts/init-db.js
const mongoose = require('mongoose');
const User = require('../backend/src/models/User');
const Goal = require('../backend/src/models/Goal');

async function initDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    
    // 创建索引
    await User.createIndexes();
    await Goal.createIndexes();
    
    // 创建默认管理员用户
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin'
    });
    
    await adminUser.save();
    console.log('数据库初始化完成');
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    await mongoose.disconnect();
  }
}

initDatabase();
```

#### Redis初始化
```bash
# 启动Redis服务
redis-server

# 测试连接
redis-cli ping
```

### 5. 启动服务

#### 开发环境启动
```bash
# 启动所有服务
npm run dev

# 或者分别启动
npm run dev:frontend    # 启动前端 (端口3000)
npm run dev:backend     # 启动后端 (端口3001)
npm run dev:ai          # 启动AI服务 (端口8001)
```

#### 生产环境启动
```bash
# 构建项目
npm run build

# 启动生产服务
npm run start:prod
```

## Docker部署

### 1. Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_BASE_URL=http://backend:3000/api

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
      - redis
      - ai-service
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/task_assistant
      - REDIS_URL=redis://redis:6379
      - AI_SERVICE_URL=http://ai-service:8001
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  # AI服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./ai-models:/app/models

  # MongoDB数据库
  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

volumes:
  mongodb_data:
  redis_data:
```

### 2. Dockerfile配置

#### 前端Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 后端Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

RUN npm run build

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

#### AI服务Dockerfile
```dockerfile
# ai-service/Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8001

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]
```

### 3. 部署命令

```bash
# 构建和启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 清理数据
docker-compose down -v
```

## 云服务部署

### 1. AWS部署

#### ECS部署配置
```json
{
  "family": "task-assistant",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/task-assistant-backend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/task-assistant",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### 部署脚本
```bash
#!/bin/bash
# deploy-aws.sh

# 构建和推送镜像
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin your-account.dkr.ecr.us-west-2.amazonaws.com

docker build -t task-assistant-backend ./backend
docker tag task-assistant-backend:latest your-account.dkr.ecr.us-west-2.amazonaws.com/task-assistant-backend:latest
docker push your-account.dkr.ecr.us-west-2.amazonaws.com/task-assistant-backend:latest

# 更新ECS服务
aws ecs update-service --cluster task-assistant-cluster --service task-assistant-service --force-new-deployment
```

### 2. 阿里云部署

#### ACK部署配置
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: task-assistant-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: task-assistant-backend
  template:
    metadata:
      labels:
        app: task-assistant-backend
    spec:
      containers:
      - name: backend
        image: registry.cn-hangzhou.aliyuncs.com/your-namespace/task-assistant-backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: mongodb-uri
---
apiVersion: v1
kind: Service
metadata:
  name: task-assistant-backend-service
spec:
  selector:
    app: task-assistant-backend
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

## 监控和日志

### 1. 应用监控

#### Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'task-assistant-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: '/metrics'

  - job_name: 'task-assistant-ai'
    static_configs:
      - targets: ['ai-service:8001']
    metrics_path: '/metrics'
```

#### Grafana仪表板
```json
{
  "dashboard": {
    "title": "任务辅助达成机监控",
    "panels": [
      {
        "title": "API响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "http_request_duration_seconds{job=\"task-assistant-backend\"}"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])"
          }
        ]
      }
    ]
  }
}
```

### 2. 日志管理

#### ELK Stack配置
```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "task-assistant" {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "task-assistant-%{+YYYY.MM.dd}"
  }
}
```

## 性能优化

### 1. 数据库优化

```javascript
// 数据库连接池配置
const mongoose = require('mongoose');

mongoose.connect(process.env.MONGODB_URI, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false,
  bufferMaxEntries: 0
});

// 索引优化
db.inspirations.createIndex({ "userId": 1, "createdAt": -1 });
db.goals.createIndex({ "userId": 1, "status": 1, "deadline": 1 });
db.tasks.createIndex({ "goalId": 1, "priority": 1 });
```

### 2. 缓存策略

```javascript
// Redis缓存配置
const redis = require('redis');
const client = redis.createClient({
  url: process.env.REDIS_URL,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('Redis服务器拒绝连接');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('重试时间已用尽');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    return Math.min(options.attempt * 100, 3000);
  }
});

// 缓存中间件
const cacheMiddleware = (duration = 300) => {
  return async (req, res, next) => {
    const key = `cache:${req.originalUrl}`;
    
    try {
      const cached = await client.get(key);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
      
      res.sendResponse = res.json;
      res.json = (body) => {
        client.setex(key, duration, JSON.stringify(body));
        res.sendResponse(body);
      };
      
      next();
    } catch (error) {
      next();
    }
  };
};
```

---

**文档版本**: v1.0
**最后更新**: 2025-08-28
